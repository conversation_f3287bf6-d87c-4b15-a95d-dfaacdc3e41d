/**
 * أنماط CSS لمكون SoundSelector
 * يحتوي على الانيميشن والتأثيرات البصرية
 */

/* انيميشن شريط التقدم */
@keyframes progressBar {
  from {
    width: 0%;
    opacity: 0.8;
  }
  to {
    width: 100%;
    opacity: 1;
  }
}

/* تأثيرات إضافية للمكون */
.sound-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sound-card:hover {
  transform: translateY(-2px);
}

.sound-card.selected {
  box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.3);
}

.sound-card.selected:hover {
  box-shadow: 0 15px 35px -5px rgba(59, 130, 246, 0.4);
}

/* تأثير زر التشغيل */
.play-button {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.play-button:hover {
  transform: scale(1.1);
}

.play-button:active {
  transform: scale(0.95);
}

.play-button.playing {
  animation: playingPulse 2s ease-in-out infinite;
}

@keyframes playingPulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(34, 197, 94, 0);
  }
}

/* تأثير شريط التقدم */
.progress-bar {
  position: relative;
  overflow: hidden;
}

.progress-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* تأثيرات الفئات */
.category-classic {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
}

.category-modern {
  background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%);
}

.category-gentle {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
}

.category-alert {
  background: linear-gradient(135deg, #fed7aa 0%, #fdba74 100%);
}

/* تأثيرات الوضع المظلم */
@media (prefers-color-scheme: dark) {
  .category-classic {
    background: linear-gradient(135deg, #1e3a8a 0%, #1d4ed8 100%);
  }

  .category-modern {
    background: linear-gradient(135deg, #581c87 0%, #7c3aed 100%);
  }

  .category-gentle {
    background: linear-gradient(135deg, #14532d 0%, #16a34a 100%);
  }

  .category-alert {
    background: linear-gradient(135deg, #9a3412 0%, #ea580c 100%);
  }
}

/* انيميشن ظهور البطاقات */
.sound-card {
  animation: fadeInUp 0.5s ease-out forwards;
}

.sound-card:nth-child(1) { animation-delay: 0.1s; }
.sound-card:nth-child(2) { animation-delay: 0.2s; }
.sound-card:nth-child(3) { animation-delay: 0.3s; }
.sound-card:nth-child(4) { animation-delay: 0.4s; }
.sound-card:nth-child(5) { animation-delay: 0.5s; }
.sound-card:nth-child(6) { animation-delay: 0.6s; }
.sound-card:nth-child(7) { animation-delay: 0.7s; }

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* تأثير التحديد */
.selection-indicator {
  animation: selectionPulse 0.6s ease-out;
}

@keyframes selectionPulse {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* تحسينات الأداء */
.sound-card,
.play-button,
.progress-bar {
  will-change: transform;
}

/* تأثيرات التفاعل */
.sound-card:focus-within {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.play-button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}
