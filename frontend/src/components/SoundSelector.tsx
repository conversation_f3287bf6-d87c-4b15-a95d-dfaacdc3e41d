/**
 * مكون اختيار صوت التنبيه
 * يوفر واجهة لاختيار ومعاينة أصوات التنبيه المختلفة
 */

import React, { useState, useEffect } from 'react';
import {
  FiPlay,
  FiPause,
  FiCheck,
  FiMusic,
  FiVolume2,
  FiRefreshCw
} from 'react-icons/fi';
import { SoundOption, AVAILABLE_SOUNDS } from '../types/audioTypes';
import { chatAudioService } from '../services/chatAudioService';
import '../styles/soundSelector.css';

interface SoundSelectorProps {
  selectedSoundId: string;
  onSoundChange: (soundId: string) => void;
  disabled?: boolean;
  className?: string;
}

const SoundSelector: React.FC<SoundSelectorProps> = ({
  selectedSoundId,
  onSoundChange,
  disabled = false,
  className = ''
}) => {
  const [playingSound, setPlayingSound] = useState<string | null>(null);
  const [loadingSound, setLoadingSound] = useState<string | null>(null);

  // تنظيف التشغيل عند إلغاء المكون
  useEffect(() => {
    return () => {
      setPlayingSound(null);
      setLoadingSound(null);
    };
  }, []);

  // تشغيل صوت معاينة
  const playPreview = async (soundId: string) => {
    if (disabled || playingSound === soundId) return;

    setLoadingSound(soundId);
    setPlayingSound(null);

    try {
      // تغيير الصوت مؤقتاً للمعاينة
      const currentSoundId = chatAudioService.getCurrentSound()?.id;
      chatAudioService.changeSoundId(soundId);
      
      // تشغيل الصوت
      const success = await chatAudioService.testSound();
      
      if (success) {
        setPlayingSound(soundId);
        
        // إيقاف التشغيل بعد مدة الصوت
        const sound = AVAILABLE_SOUNDS.find(s => s.id === soundId);
        const duration = sound?.duration || 1;
        
        setTimeout(() => {
          setPlayingSound(null);
        }, duration * 1000);
      }
      
      // إرجاع الصوت الأصلي
      if (currentSoundId && currentSoundId !== soundId) {
        chatAudioService.changeSoundId(currentSoundId);
      }
      
    } catch (error) {
      console.error('خطأ في تشغيل المعاينة:', error);
    } finally {
      setLoadingSound(null);
    }
  };

  // اختيار صوت
  const selectSound = (soundId: string) => {
    if (disabled) return;
    onSoundChange(soundId);
  };

  // الحصول على أيقونة الفئة
  const getCategoryIcon = (category: SoundOption['category']) => {
    switch (category) {
      case 'classic':
        return '🎵';
      case 'modern':
        return '🎶';
      case 'gentle':
        return '🌸';
      case 'alert':
        return '⚡';
      default:
        return '🔊';
    }
  };

  // الحصول على لون الفئة
  const getCategoryColor = (category: SoundOption['category']) => {
    switch (category) {
      case 'classic':
        return 'text-blue-600 dark:text-blue-400';
      case 'modern':
        return 'text-purple-600 dark:text-purple-400';
      case 'gentle':
        return 'text-green-600 dark:text-green-400';
      case 'alert':
        return 'text-orange-600 dark:text-orange-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* عنوان القسم */}
      <div className="flex items-center gap-2 mb-4">
        <FiMusic className="w-5 h-5 text-primary-600 dark:text-primary-400" />
        <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
          اختيار صوت التنبيه
        </h4>
      </div>

      {/* قائمة الأصوات */}
      <div className="grid gap-3">
        {AVAILABLE_SOUNDS.map((sound) => {
          const isSelected = selectedSoundId === sound.id;
          const isPlaying = playingSound === sound.id;
          const isLoading = loadingSound === sound.id;

          return (
            <div
              key={sound.id}
              className={`
                relative p-4 rounded-xl border-2 transition-all duration-200 cursor-pointer
                ${isSelected 
                  ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20' 
                  : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                }
                ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:shadow-md'}
              `}
              onClick={() => selectSound(sound.id)}
            >
              {/* علامة الاختيار */}
              {isSelected && (
                <div className="absolute top-2 right-2">
                  <div className="w-6 h-6 bg-primary-500 rounded-full flex items-center justify-center">
                    <FiCheck className="w-4 h-4 text-white" />
                  </div>
                </div>
              )}

              <div className="flex items-center justify-between">
                {/* معلومات الصوت */}
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <span className="text-2xl">{getCategoryIcon(sound.category)}</span>
                    <div>
                      <h5 className="font-medium text-gray-900 dark:text-gray-100">
                        {sound.nameAr}
                      </h5>
                      <p className="text-xs text-gray-600 dark:text-gray-400">
                        {sound.descriptionAr}
                      </p>
                    </div>
                  </div>
                  
                  {/* معاينة ومدة */}
                  <div className="flex items-center gap-4 text-xs">
                    <span className={`font-medium ${getCategoryColor(sound.category)}`}>
                      {sound.preview}
                    </span>
                    <span className="text-gray-500 dark:text-gray-400">
                      {sound.duration}ث
                    </span>
                  </div>
                </div>

                {/* زر التشغيل */}
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    playPreview(sound.id);
                  }}
                  disabled={disabled || isLoading}
                  className={`
                    flex items-center justify-center w-10 h-10 rounded-full
                    transition-all duration-200 ml-3
                    ${isPlaying 
                      ? 'bg-green-500 text-white shadow-lg' 
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                    }
                    ${disabled ? 'cursor-not-allowed opacity-50' : 'hover:scale-105 active:scale-95'}
                  `}
                >
                  {isLoading ? (
                    <FiRefreshCw className="w-4 h-4 animate-spin" />
                  ) : isPlaying ? (
                    <FiVolume2 className="w-4 h-4 animate-pulse" />
                  ) : (
                    <FiPlay className="w-4 h-4" />
                  )}
                </button>
              </div>

              {/* شريط التشغيل */}
              {isPlaying && (
                <div className="mt-3">
                  <div className="w-full h-1 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-green-500 rounded-full transition-all duration-1000 ease-linear"
                      style={{
                        width: '100%',
                        animationName: 'progressBar',
                        animationDuration: `${sound.duration}s`,
                        animationTimingFunction: 'linear',
                        animationFillMode: 'forwards'
                      }}
                    />
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* معلومات إضافية */}
      <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg border border-blue-200 dark:border-blue-800">
        <div className="flex items-start gap-2">
          <FiMusic className="w-4 h-4 text-blue-600 dark:text-blue-400 mt-0.5" />
          <div className="text-xs text-blue-700 dark:text-blue-300">
            <p className="font-medium mb-1">نصائح:</p>
            <ul className="space-y-1">
              <li>• اضغط على الصوت لاختياره</li>
              <li>• اضغط على زر التشغيل للمعاينة</li>
              <li>• جرب أصوات مختلفة لتجد المناسب لك</li>
            </ul>
          </div>
        </div>
      </div>


    </div>
  );
};

export default SoundSelector;
