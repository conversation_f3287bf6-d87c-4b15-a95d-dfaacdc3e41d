/**
 * مكون إعدادات الصوت للمحادثة الفورية
 * يوفر واجهة لتخصيص إعدادات الصوت في نظام المحادثة
 */

import React, { useState, useEffect } from 'react';
import {
  FiVolumeX,
  FiVolume1,
  FiVolume2,
  FiVolumeOff,
  FiPlay,
  FiSettings,
  FiCheck,
  FiRefreshCw
} from 'react-icons/fi';
import { chatNotificationService } from '../services/chatNotificationService';
import ToggleSwitch from './ToggleSwitch';
import { NumberInput } from './inputs';

interface ChatAudioSettingsProps {
  className?: string;
  settings?: Record<string, string>;
  onSettingChange?: (key: string, value: string) => void;
}

const ChatAudioSettings: React.FC<ChatAudioSettingsProps> = ({
  className = '',
  settings: parentSettings,
  onSettingChange
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [testResult, setTestResult] = useState<'success' | 'error' | null>(null);
  const [audioSettings, setAudioSettings] = useState({
    enabled: true,
    volume: 70
  });

  // تحميل الإعدادات الحالية
  useEffect(() => {
    loadAudioSettings();
  }, [parentSettings]);

  const loadAudioSettings = () => {
    if (parentSettings) {
      // استخدام الإعدادات من الصفحة الرئيسية
      setAudioSettings({
        enabled: parentSettings.chat_sound_enabled === 'true',
        volume: parseInt(parentSettings.chat_sound_volume) || 70
      });
    } else {
      // تحميل من خدمة الصوت مباشرة
      const currentSettings = chatNotificationService.getAudioSettings();
      setAudioSettings({
        enabled: currentSettings.enabled,
        volume: Math.round(currentSettings.volume * 100)
      });
    }
  };

  const handleEnabledChange = (enabled: boolean) => {
    const newSettings = { ...audioSettings, enabled };
    setAudioSettings(newSettings);

    // تحديث خدمة الصوت
    chatNotificationService.updateAudioSettings({ enabled });

    // إشعار الصفحة الرئيسية
    if (onSettingChange) {
      onSettingChange('chat_sound_enabled', enabled.toString());
    }
  };

  const handleVolumeChange = (volume: number) => {
    const clampedVolume = Math.max(0, Math.min(100, volume));
    const newSettings = { ...audioSettings, volume: clampedVolume };
    setAudioSettings(newSettings);

    // تحديث خدمة الصوت (تحويل إلى 0.0-1.0)
    chatNotificationService.updateAudioSettings({ volume: clampedVolume / 100 });

    // إشعار الصفحة الرئيسية
    if (onSettingChange) {
      onSettingChange('chat_sound_volume', clampedVolume.toString());
    }
  };

  const testSound = async () => {
    setIsTesting(true);
    setTestResult(null);

    try {
      const success = await chatNotificationService.testAudioNotification();
      setTestResult(success ? 'success' : 'error');
    } catch (error) {
      console.error('خطأ في اختبار الصوت:', error);
      setTestResult('error');
    } finally {
      setIsTesting(false);
      
      // إخفاء النتيجة بعد 3 ثوان
      setTimeout(() => setTestResult(null), 3000);
    }
  };

  const getVolumeIcon = () => {
    if (!audioSettings.enabled) return <FiVolumeOff className="text-gray-400" />;
    if (audioSettings.volume === 0) return <FiVolumeX className="text-red-500" />;
    if (audioSettings.volume < 50) return <FiVolume1 className="text-yellow-500" />;
    return <FiVolume2 className="text-green-500" />;
  };

  const getVolumeColor = () => {
    if (!audioSettings.enabled) return 'bg-gray-200';
    if (audioSettings.volume < 30) return 'bg-red-500';
    if (audioSettings.volume < 70) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* عنوان القسم */}
      <div className="flex items-center gap-3 mb-6">
        <div className="p-2 bg-primary-100 dark:bg-primary-900/30 rounded-lg">
          <FiSettings className="w-5 h-5 text-primary-600 dark:text-primary-400" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            إعدادات الصوت للمحادثة
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            تخصيص أصوات التنبيه عند وصول رسائل جديدة
          </p>
        </div>
      </div>

      {/* تفعيل/تعطيل الصوت */}
      <div className="bg-white dark:bg-gray-800 p-4 rounded-xl border-2 border-gray-200 dark:border-gray-600 h-12 flex items-center">
        <ToggleSwitch
          id="chatSoundEnabled"
          checked={audioSettings.enabled}
          onChange={handleEnabledChange}
          label="تفعيل أصوات التنبيه"
          className="w-full"
        />
      </div>

      {/* مستوى الصوت */}
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
            مستوى الصوت
          </label>
          <div className="flex items-center gap-2">
            {getVolumeIcon()}
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {audioSettings.volume}%
            </span>
          </div>
        </div>

        <div className="space-y-3">
          {/* شريط مستوى الصوت */}
          <div className="relative">
            <div className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
              <div
                className={`h-full transition-all duration-300 ${getVolumeColor()}`}
                style={{ width: `${audioSettings.enabled ? audioSettings.volume : 0}%` }}
              />
            </div>
            <input
              type="range"
              min="0"
              max="100"
              value={audioSettings.volume}
              onChange={(e) => handleVolumeChange(parseInt(e.target.value))}
              disabled={!audioSettings.enabled}
              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer disabled:cursor-not-allowed"
            />
          </div>

          {/* إدخال رقمي للمستوى */}
          <div className="w-32">
            <NumberInput
              name="volume"
              value={audioSettings.volume.toString()}
              onChange={(value) => handleVolumeChange(parseInt(value) || 0)}
              min={0}
              max={100}
              disabled={!audioSettings.enabled}
              placeholder="0-100"
              className="text-center"
            />
          </div>
        </div>
      </div>

      {/* اختبار الصوت */}
      <div className="bg-gray-50 dark:bg-gray-800/50 p-4 rounded-xl border border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
              اختبار الصوت
            </h4>
            <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
              اضغط لتشغيل صوت تجريبي
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            {testResult && (
              <div className={`flex items-center gap-1 text-sm ${
                testResult === 'success' 
                  ? 'text-green-600 dark:text-green-400' 
                  : 'text-red-600 dark:text-red-400'
              }`}>
                {testResult === 'success' ? (
                  <>
                    <FiCheck className="w-4 h-4" />
                    <span>تم التشغيل</span>
                  </>
                ) : (
                  <>
                    <FiVolumeOff className="w-4 h-4" />
                    <span>فشل التشغيل</span>
                  </>
                )}
              </div>
            )}
            
            <button
              onClick={testSound}
              disabled={isTesting || !audioSettings.enabled}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-all ${
                audioSettings.enabled
                  ? 'bg-primary-600 hover:bg-primary-700 text-white shadow-md hover:shadow-lg'
                  : 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'
              }`}
            >
              {isTesting ? (
                <>
                  <FiRefreshCw className="w-4 h-4 animate-spin" />
                  <span>جاري التشغيل...</span>
                </>
              ) : (
                <>
                  <FiPlay className="w-4 h-4" />
                  <span>اختبار الصوت</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* معلومات إضافية */}
      <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-xl border border-blue-200 dark:border-blue-800">
        <div className="flex items-start gap-3">
          <div className="p-1 bg-blue-100 dark:bg-blue-900/50 rounded">
            <FiSettings className="w-4 h-4 text-blue-600 dark:text-blue-400" />
          </div>
          <div className="text-sm">
            <h5 className="font-medium text-blue-900 dark:text-blue-100 mb-1">
              ملاحظات مهمة
            </h5>
            <ul className="text-blue-700 dark:text-blue-300 space-y-1 text-xs">
              <li>• سيتم تشغيل الصوت فقط عند وصول رسائل جديدة ونافذة المحادثة مغلقة</li>
              <li>• يمكن تعديل مستوى الصوت في أي وقت</li>
              <li>• الإعدادات محفوظة محلياً في المتصفح</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatAudioSettings;
