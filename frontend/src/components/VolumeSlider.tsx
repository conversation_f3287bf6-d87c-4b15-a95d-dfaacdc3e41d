/**
 * مكون شريط تمرير مستوى الصوت
 * يوفر واجهة سهلة وجميلة لتحديد مستوى الصوت
 */

import React, { useState, useRef, useEffect } from 'react';
import {
  FiVolumeX,
  FiVolume1,
  FiVolume2,
  FiVolume
} from 'react-icons/fi';

interface VolumeSliderProps {
  value: number; // 0-100
  onChange: (value: number) => void;
  disabled?: boolean;
  className?: string;
  showIcon?: boolean;
  showValue?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

const VolumeSlider: React.FC<VolumeSliderProps> = ({
  value,
  onChange,
  disabled = false,
  className = '',
  showIcon = true,
  showValue = true,
  size = 'md'
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [localValue, setLocalValue] = useState(value);
  const sliderRef = useRef<HTMLDivElement>(null);

  // تحديث القيمة المحلية عند تغيير القيمة الخارجية
  useEffect(() => {
    setLocalValue(value);
  }, [value]);

  // الحصول على أيقونة الصوت المناسبة
  const getVolumeIcon = () => {
    if (disabled) return <FiVolume className="text-gray-400" />;
    if (localValue === 0) return <FiVolumeX className="text-red-500" />;
    if (localValue < 30) return <FiVolume1 className="text-yellow-500" />;
    if (localValue < 70) return <FiVolume1 className="text-blue-500" />;
    return <FiVolume2 className="text-green-500" />;
  };

  // الحصول على لون الشريط
  const getSliderColor = () => {
    if (disabled) return 'bg-gray-300 dark:bg-gray-600';
    if (localValue === 0) return 'bg-red-500';
    if (localValue < 30) return 'bg-red-400';
    if (localValue < 70) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  // الحصول على أحجام المكون
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return {
          container: 'h-6',
          track: 'h-1.5',
          thumb: 'w-4 h-4',
          icon: 'w-4 h-4',
          text: 'text-xs'
        };
      case 'lg':
        return {
          container: 'h-10',
          track: 'h-3',
          thumb: 'w-6 h-6',
          icon: 'w-6 h-6',
          text: 'text-base'
        };
      default: // md
        return {
          container: 'h-8',
          track: 'h-2',
          thumb: 'w-5 h-5',
          icon: 'w-5 h-5',
          text: 'text-sm'
        };
    }
  };

  const sizeClasses = getSizeClasses();

  // حساب القيمة من موضع الماوس
  const calculateValueFromPosition = (clientX: number) => {
    if (!sliderRef.current) return localValue;

    const rect = sliderRef.current.getBoundingClientRect();
    const percentage = Math.max(0, Math.min(1, (clientX - rect.left) / rect.width));
    return Math.round(percentage * 100);
  };

  // معالجة بداية السحب
  const handleMouseDown = (e: React.MouseEvent) => {
    if (disabled) return;

    setIsDragging(true);
    const newValue = calculateValueFromPosition(e.clientX);
    setLocalValue(newValue);
    onChange(newValue);

    // منع تحديد النص
    e.preventDefault();
  };

  // معالجة السحب
  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging || disabled) return;

    const newValue = calculateValueFromPosition(e.clientX);
    setLocalValue(newValue);
    onChange(newValue);
  };

  // معالجة انتهاء السحب
  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // إضافة مستمعي الأحداث للسحب
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);

      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging]);

  // معالجة النقر المباشر على الشريط
  const handleTrackClick = (e: React.MouseEvent) => {
    if (disabled) return;

    const newValue = calculateValueFromPosition(e.clientX);
    setLocalValue(newValue);
    onChange(newValue);
  };

  return (
    <div className={`flex items-center gap-3 ${className}`}>
      {/* أيقونة الصوت */}
      {showIcon && (
        <div className="flex-shrink-0">
          {getVolumeIcon()}
        </div>
      )}

      {/* شريط التمرير */}
      <div className={`flex-1 relative ${sizeClasses.container} flex items-center`}>
        {/* المسار الخلفي */}
        <div
          ref={sliderRef}
          className={`
            relative w-full ${sizeClasses.track} 
            bg-gray-200 dark:bg-gray-700 rounded-full 
            cursor-pointer transition-all duration-200
            ${disabled ? 'cursor-not-allowed opacity-50' : 'hover:bg-gray-300 dark:hover:bg-gray-600'}
          `}
          onClick={handleTrackClick}
        >
          {/* المسار النشط */}
          <div
            className={`
              absolute left-0 top-0 ${sizeClasses.track} rounded-full 
              transition-all duration-200 ${getSliderColor()}
            `}
            style={{ width: `${disabled ? 0 : localValue}%` }}
          />

          {/* المقبض */}
          <div
            className={`
              absolute top-1/2 transform -translate-y-1/2 ${sizeClasses.thumb}
              ${getSliderColor()} rounded-full shadow-lg
              transition-all duration-200 cursor-grab
              ${isDragging ? 'cursor-grabbing scale-110 shadow-xl' : ''}
              ${disabled ? 'cursor-not-allowed opacity-50' : 'hover:scale-105'}
            `}
            style={{ left: `calc(${disabled ? 0 : localValue}% - ${parseInt(sizeClasses.thumb.split(' ')[0].replace('w-', '')) * 0.25}rem)` }}
            onMouseDown={handleMouseDown}
          >
            {/* نقطة في المنتصف */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-1 h-1 bg-white rounded-full opacity-80" />
            </div>
          </div>
        </div>
      </div>

      {/* عرض القيمة */}
      {showValue && (
        <div className={`flex-shrink-0 min-w-[3rem] text-right ${sizeClasses.text}`}>
          <span className={`font-medium ${
            disabled 
              ? 'text-gray-400' 
              : 'text-gray-700 dark:text-gray-300'
          }`}>
            {localValue}%
          </span>
        </div>
      )}
    </div>
  );
};

export default VolumeSlider;
