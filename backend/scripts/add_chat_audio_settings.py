#!/usr/bin/env python3
"""
إضافة إعدادات الصوت للمحادثة إلى قاعدة البيانات
"""

import sys
import os

# إضافة مسار المشروع إلى Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from database.connection import get_db
from models.setting import Setting

def add_chat_audio_settings():
    """إضافة إعدادات الصوت للمحادثة"""
    
    # الحصول على جلسة قاعدة البيانات
    db_gen = get_db()
    db: Session = next(db_gen)
    
    # إعدادات الصوت للمحادثة
    audio_settings = [
        {
            'key': 'chat_sound_enabled',
            'value': 'true',
            'description': 'تفعيل أصوات التنبيه للرسائل الجديدة في المحادثة'
        },
        {
            'key': 'chat_sound_volume',
            'value': '0.7',
            'description': 'مستوى صوت التنبيهات (0.0 - 1.0)'
        },
        {
            'key': 'chat_sound_id',
            'value': 'notification',
            'description': 'معرف صوت التنبيه المختار'
        }
    ]
    
    try:
        for setting_data in audio_settings:
            # التحقق من وجود الإعداد مسبقاً
            existing_setting = db.query(Setting).filter(
                Setting.key == setting_data['key']
            ).first()
            
            if existing_setting:
                print(f"✅ الإعداد '{setting_data['key']}' موجود مسبقاً.")
                print(f"   القيمة الحالية: {existing_setting.value}")
                continue
            
            # إنشاء الإعداد الجديد
            new_setting = Setting(
                key=setting_data['key'],
                value=setting_data['value'],
                description=setting_data['description']
            )
            
            db.add(new_setting)
            print(f"✅ تم إضافة الإعداد '{setting_data['key']}' بنجاح.")
            print(f"   القيمة الافتراضية: {setting_data['value']}")
            print(f"   الوصف: {setting_data['description']}")
        
        # حفظ التغييرات
        db.commit()
        print("\n🎉 تم إضافة جميع إعدادات الصوت للمحادثة بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في إضافة إعدادات الصوت: {str(e)}")
        db.rollback()
    finally:
        db.close()

def main():
    """الدالة الرئيسية"""
    print("🔊 إضافة إعدادات الصوت للمحادثة...")
    print("=" * 50)
    
    add_chat_audio_settings()
    
    print("\n" + "=" * 50)
    print("✅ انتهت عملية إضافة إعدادات الصوت.")

if __name__ == "__main__":
    main()
